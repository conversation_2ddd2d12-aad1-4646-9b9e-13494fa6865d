import { User, AttendanceRecord } from "@/stores/app-store";
import {
  getCollegeCategory,
  getCategoryDisplayName,
  CollegeCategory,
  SCIENCE_COLLEGES,
  EDUCATION_COLLEGES,
} from "@/lib/mock-data";

// Re-export types and functions for convenience
export type { CollegeCategory };
export { getCollegeCategory, getCategoryDisplayName };

// Interface for college-based statistics
export interface CollegeStats {
  scienceMembers: number;
  educationMembers: number;
  otherMembers: number;
  sciencePresent: number;
  educationPresent: number;
  otherPresent: number;
  scienceAttendanceRate: number;
  educationAttendanceRate: number;
  otherAttendanceRate: number;
}

// Interface for detailed college analytics
export interface CollegeAnalytics {
  category: CollegeCategory;
  displayName: string;
  totalMembers: number;
  presentToday: number;
  attendanceRate: number;
  consistentAttendees: number;
  needsFollowUp: number;
  weeklyAttendance: number;
  monthlyAttendance: number;
}

/**
 * Calculate college-based statistics for dashboard
 */
export function calculateCollegeStats(
  users: User[],
  attendanceRecords: AttendanceRecord[],
  targetDate?: string
): CollegeStats {
  const today = targetDate || new Date().toISOString().split("T")[0];

  // Categorize users by college
  const scienceUsers = users.filter((user) =>
    SCIENCE_COLLEGES.includes(user.college)
  );
  const educationUsers = users.filter((user) =>
    EDUCATION_COLLEGES.includes(user.college)
  );
  const otherUsers = users.filter(
    (user) =>
      !SCIENCE_COLLEGES.includes(user.college) &&
      !EDUCATION_COLLEGES.includes(user.college)
  );

  // Get today's attendance for each category
  const todayRecords = attendanceRecords.filter(
    (record) => record.date === today
  );

  const sciencePresent = todayRecords.filter(
    (record) =>
      record.present && scienceUsers.some((user) => user.id === record.user_id)
  ).length;

  const educationPresent = todayRecords.filter(
    (record) =>
      record.present &&
      educationUsers.some((user) => user.id === record.user_id)
  ).length;

  const otherPresent = todayRecords.filter(
    (record) =>
      record.present && otherUsers.some((user) => user.id === record.user_id)
  ).length;

  // Calculate attendance rates
  const scienceAttendanceRate =
    scienceUsers.length > 0
      ? Math.round((sciencePresent / scienceUsers.length) * 100)
      : 0;

  const educationAttendanceRate =
    educationUsers.length > 0
      ? Math.round((educationPresent / educationUsers.length) * 100)
      : 0;

  const otherAttendanceRate =
    otherUsers.length > 0
      ? Math.round((otherPresent / otherUsers.length) * 100)
      : 0;

  return {
    scienceMembers: scienceUsers.length,
    educationMembers: educationUsers.length,
    otherMembers: otherUsers.length,
    sciencePresent,
    educationPresent,
    otherPresent,
    scienceAttendanceRate,
    educationAttendanceRate,
    otherAttendanceRate,
  };
}

/**
 * Get detailed analytics for a specific college category
 */
export function getCollegeCategoryAnalytics(
  category: CollegeCategory,
  users: User[],
  attendanceRecords: AttendanceRecord[]
): CollegeAnalytics {
  // Filter users by category
  const categoryUsers = users.filter(
    (user) => getCollegeCategory(user.college) === category
  );

  const today = new Date().toISOString().split("T")[0];
  const weekStart = new Date();
  weekStart.setDate(weekStart.getDate() - 7);
  const monthStart = new Date();
  monthStart.setDate(monthStart.getDate() - 30);

  // Today's attendance
  const todayRecords = attendanceRecords.filter(
    (record) => record.date === today
  );
  const presentToday = todayRecords.filter(
    (record) =>
      record.present && categoryUsers.some((user) => user.id === record.user_id)
  ).length;

  const attendanceRate =
    categoryUsers.length > 0
      ? Math.round((presentToday / categoryUsers.length) * 100)
      : 0;

  // Weekly attendance
  const weekRecords = attendanceRecords.filter((record) => {
    const recordDate = new Date(record.date);
    return (
      recordDate >= weekStart &&
      recordDate <= new Date() &&
      categoryUsers.some((user) => user.id === record.user_id)
    );
  });
  const weeklyPresent = weekRecords.filter((r) => r.present).length;
  const weeklyTotal = weekRecords.length;
  const weeklyAttendance =
    weeklyTotal > 0 ? Math.round((weeklyPresent / weeklyTotal) * 100) : 0;

  // Monthly attendance
  const monthRecords = attendanceRecords.filter((record) => {
    const recordDate = new Date(record.date);
    return (
      recordDate >= monthStart &&
      recordDate <= new Date() &&
      categoryUsers.some((user) => user.id === record.user_id)
    );
  });
  const monthlyPresent = monthRecords.filter((r) => r.present).length;
  const monthlyTotal = monthRecords.length;
  const monthlyAttendance =
    monthlyTotal > 0 ? Math.round((monthlyPresent / monthlyTotal) * 100) : 0;

  // Calculate user attendance rates for consistent attendees and red flags
  const userAttendanceRates = categoryUsers.map((user) => {
    const userRecords = attendanceRecords.filter((r) => r.user_id === user.id);
    const userPresent = userRecords.filter((r) => r.present).length;
    const userTotal = userRecords.length;
    return {
      userId: user.id,
      rate: userTotal > 0 ? (userPresent / userTotal) * 100 : 0,
    };
  });

  const consistentAttendees = userAttendanceRates.filter(
    (u) => u.rate >= 80
  ).length;
  const needsFollowUp = userAttendanceRates.filter(
    (u) => u.rate < 50 && u.rate > 0
  ).length;

  return {
    category,
    displayName: getCategoryDisplayName(category),
    totalMembers: categoryUsers.length,
    presentToday,
    attendanceRate,
    consistentAttendees,
    needsFollowUp,
    weeklyAttendance,
    monthlyAttendance,
  };
}

/**
 * Get all college categories analytics
 */
export function getAllCollegeAnalytics(
  users: User[],
  attendanceRecords: AttendanceRecord[]
): CollegeAnalytics[] {
  const categories: CollegeCategory[] = ["science", "education", "others"];
  return categories.map((category) =>
    getCollegeCategoryAnalytics(category, users, attendanceRecords)
  );
}

/**
 * Filter users by college category
 */
export function filterUsersByCollegeCategory(
  users: User[],
  category: CollegeCategory | "all"
): User[] {
  if (category === "all") {
    return users;
  }
  return users.filter((user) => getCollegeCategory(user.college) === category);
}

/**
 * Get college filter options for UI components
 */
export function getCollegeFilterOptions() {
  return [
    { value: "all", label: "جميع الكليات" },
    { value: "science", label: "كلية العلوم" },
    { value: "education", label: "كلية التربية" },
    { value: "others", label: "الكليات الأخرى" },
  ];
}
