"use client";

import type React from "react";

import { createContext, useContext, useEffect, useState } from "react";

// Local User type - no longer using Supabase
export interface User {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
  email_confirmed_at: string;
  last_sign_in_at: string;
  app_metadata: Record<string, any>;
  user_metadata: Record<string, any>;
  aud: string;
  role: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Check for demo auth on mount
  useEffect(() => {
    const demoAuth = localStorage.getItem("demo-auth");
    if (demoAuth) {
      try {
        const mockUser = JSON.parse(demoAuth);
        setUser(mockUser);
      } catch (error) {
        localStorage.removeItem("demo-auth");
      }
    }
    setLoading(false);
  }, []);

  const signIn = async (email: string, password: string) => {
    // Demo authentication for testing
    if (email === "<EMAIL>" && password === "password") {
      // Create a mock user for demo purposes
      const mockUser = {
        id: "demo-user-id",
        email: "<EMAIL>",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        email_confirmed_at: new Date().toISOString(),
        last_sign_in_at: new Date().toISOString(),
        app_metadata: {},
        user_metadata: {},
        aud: "authenticated",
        role: "authenticated",
      } as User;

      setUser(mockUser);

      // Store in localStorage for persistence
      localStorage.setItem("demo-auth", JSON.stringify(mockUser));

      return { error: null };
    }

    // Return error for invalid credentials
    return { error: "Invalid credentials" };
  };

  const signOut = async () => {
    // Clear demo auth
    localStorage.removeItem("demo-auth");
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, loading, signIn, signOut }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
