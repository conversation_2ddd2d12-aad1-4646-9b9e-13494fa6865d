"use client";

import { useState, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart3,
  Download,
  Calendar,
  TrendingUp,
  FileText,
  PieChart,
  Activity,
  Award,
  AlertTriangle,
} from "lucide-react";
import { useAppStore } from "@/stores/app-store";
import { useRTL } from "@/contexts/rtl-context";
import { cn } from "@/lib/utils";
import { format, subDays, startOfWeek } from "date-fns";
import { ar } from "date-fns/locale";

export default function ReportsPage() {
  const { users, attendanceRecords } = useAppStore();
  const { isRTL, direction } = useRTL();

  const [selectedPeriod, setSelectedPeriod] = useState("week");
  const [selectedYear, setSelectedYear] = useState("all");

  // Calculate report data
  const reportData = useMemo(() => {
    const today = new Date();
    let startDate: Date;

    switch (selectedPeriod) {
      case "week":
        startDate = startOfWeek(today, { weekStartsOn: 1 });
        break;
      case "month":
        startDate = new Date(today.getFullYear(), today.getMonth(), 1);
        break;
      case "year":
        startDate = new Date(today.getFullYear(), 0, 1);
        break;
      default:
        startDate = subDays(today, 30);
    }

    const filteredRecords = attendanceRecords.filter((record) => {
      const recordDate = new Date(record.date);
      const yearFilter =
        selectedYear === "all" ||
        users.find((u) => u.id === record.user_id)?.year.toString() ===
          selectedYear;

      return recordDate >= startDate && recordDate <= today && yearFilter;
    });

    // Attendance by date
    const attendanceByDate = filteredRecords.reduce((acc, record) => {
      const date = record.date;
      if (!acc[date]) {
        acc[date] = { present: 0, absent: 0, total: 0 };
      }
      if (record.present) {
        acc[date].present++;
      } else {
        acc[date].absent++;
      }
      acc[date].total++;
      return acc;
    }, {} as Record<string, { present: number; absent: number; total: number }>);

    // Attendance by year
    const attendanceByYear = [1, 2, 3, 4].map((year) => {
      const yearUsers = users.filter((u) => u.year === year);
      const yearRecords = filteredRecords.filter((r) =>
        yearUsers.some((u) => u.id === r.user_id)
      );
      const present = yearRecords.filter((r) => r.present).length;
      const total = yearRecords.length;

      return {
        year,
        present,
        total,
        percentage: total > 0 ? Math.round((present / total) * 100) : 0,
      };
    });

    // Top attendees
    const userAttendance = users
      .map((user) => {
        const userRecords = filteredRecords.filter(
          (r) => r.user_id === user.id
        );
        const present = userRecords.filter((r) => r.present).length;
        const total = userRecords.length;

        return {
          ...user,
          present,
          total,
          percentage: total > 0 ? Math.round((present / total) * 100) : 0,
        };
      })
      .sort((a, b) => b.percentage - a.percentage);

    // Frequent absentees
    const frequentAbsentees = userAttendance.filter(
      (u) => u.total > 0 && u.percentage < 50
    );

    return {
      attendanceByDate,
      attendanceByYear,
      topAttendees: userAttendance.slice(0, 10),
      frequentAbsentees,
      totalSessions: Object.keys(attendanceByDate).length,
      averageAttendance:
        Object.values(attendanceByDate).reduce(
          (acc, day) =>
            acc + (day.total > 0 ? (day.present / day.total) * 100 : 0),
          0
        ) / Math.max(Object.keys(attendanceByDate).length, 1),
    };
  }, [users, attendanceRecords, selectedPeriod, selectedYear]);

  const exportReport = (format: "pdf" | "csv") => {
    // Mock export functionality
    console.log(`Exporting report as ${format}`);
  };

  return (
    <div className="p-6 space-y-6 page-transition font-cairo" dir={direction}>
      {/* Header */}
      <div className="animate-fade-in">
        <div
          className={cn(
            "flex items-center justify-between",
            isRTL ? "flex-row-reverse" : "flex-row"
          )}
        >
          <div>
            <div
              className={cn(
                "flex items-center gap-3 mb-2",
                isRTL ? "flex-row-reverse" : "flex-row"
              )}
            >
              <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-heading gradient-text">
                التقارير والإحصائيات
              </h1>
            </div>
            <p
              className={cn(
                "text-gray-600 text-lg text-body",
                isRTL ? "text-right" : "text-left"
              )}
            >
              تقارير شاملة عن حضور الأعضاء والإحصائيات المفصلة
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => exportReport("csv")}>
              <Download className="h-4 w-4 mr-2" />
              تصدير CSV
            </Button>
            <Button
              className="btn-gradient"
              onClick={() => exportReport("pdf")}
            >
              <FileText className="h-4 w-4 mr-2" />
              تصدير PDF
            </Button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card className="animate-scale-in">
        <CardHeader>
          <CardTitle>فلاتر التقرير</CardTitle>
          <CardDescription>اختر الفترة الزمنية والسنة الدراسية</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                الفترة الزمنية
              </label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">هذا الأسبوع</SelectItem>
                  <SelectItem value="month">هذا الشهر</SelectItem>
                  <SelectItem value="year">هذا العام</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">
                السنة الدراسية
              </label>
              <Select value={selectedYear} onValueChange={setSelectedYear}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع السنوات</SelectItem>
                  <SelectItem value="1">السنة الأولى</SelectItem>
                  <SelectItem value="2">السنة الثانية</SelectItem>
                  <SelectItem value="3">السنة الثالثة</SelectItem>
                  <SelectItem value="4">السنة الرابعة</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 animate-slide-in-right">
        <Card className="hover-lift">
          <CardHeader
            className={cn(
              "flex items-center justify-between space-y-0 pb-2",
              isRTL ? "flex-row-reverse" : "flex-row"
            )}
          >
            <CardTitle className="text-sm font-medium">
              إجمالي الجلسات
            </CardTitle>
            <Calendar className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reportData.totalSessions}</div>
            <p className="text-xs text-muted-foreground">
              جلسة في الفترة المحددة
            </p>
          </CardContent>
        </Card>

        <Card className="hover-lift">
          <CardHeader
            className={cn(
              "flex items-center justify-between space-y-0 pb-2",
              isRTL ? "flex-row-reverse" : "flex-row"
            )}
          >
            <CardTitle className="text-sm font-medium">متوسط الحضور</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {Math.round(reportData.averageAttendance)}%
            </div>
            <p className="text-xs text-muted-foreground">معدل الحضور العام</p>
          </CardContent>
        </Card>

        <Card className="hover-lift">
          <CardHeader
            className={cn(
              "flex items-center justify-between space-y-0 pb-2",
              isRTL ? "flex-row-reverse" : "flex-row"
            )}
          >
            <CardTitle className="text-sm font-medium">أفضل حضور</CardTitle>
            <Award className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {reportData.topAttendees[0]?.percentage || 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              {reportData.topAttendees[0]?.name || "لا يوجد"}
            </p>
          </CardContent>
        </Card>

        <Card className="hover-lift">
          <CardHeader
            className={cn(
              "flex items-center justify-between space-y-0 pb-2",
              isRTL ? "flex-row-reverse" : "flex-row"
            )}
          >
            <CardTitle className="text-sm font-medium">يحتاج متابعة</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {reportData.frequentAbsentees.length}
            </div>
            <p className="text-xs text-muted-foreground">عضو يحتاج متابعة</p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Reports */}
      <Tabs defaultValue="attendance" className="animate-fade-in">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="attendance">الحضور اليومي</TabsTrigger>
          <TabsTrigger value="years">الحضور بالسنوات</TabsTrigger>
          <TabsTrigger value="individuals">الحضور الفردي</TabsTrigger>
        </TabsList>

        <TabsContent value="attendance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                الحضور اليومي
              </CardTitle>
              <CardDescription>
                معدل الحضور لكل يوم في الفترة المحددة
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(reportData.attendanceByDate).map(
                  ([date, data]) => (
                    <div
                      key={date}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div>
                        <div className="font-medium">
                          {format(new Date(date), "EEEE، dd MMMM yyyy", {
                            locale: ar,
                          })}
                        </div>
                        <div className="text-sm text-gray-500">
                          {data.present} حاضر من {data.total} عضو
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={
                            data.present / data.total >= 0.8
                              ? "default"
                              : "secondary"
                          }
                        >
                          {Math.round((data.present / data.total) * 100)}%
                        </Badge>
                      </div>
                    </div>
                  )
                )}
                {Object.keys(reportData.attendanceByDate).length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد بيانات حضور في الفترة المحددة
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="years" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                الحضور بالسنوات الدراسية
              </CardTitle>
              <CardDescription>
                مقارنة معدل الحضور بين السنوات المختلفة
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportData.attendanceByYear.map((yearData) => (
                  <div
                    key={yearData.year}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold">
                          {yearData.year}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium">السنة {yearData.year}</div>
                        <div className="text-sm text-gray-500">
                          {yearData.present} حاضر من {yearData.total} جلسة
                        </div>
                      </div>
                    </div>
                    <Badge
                      variant={
                        yearData.percentage >= 80 ? "default" : "secondary"
                      }
                    >
                      {yearData.percentage}%
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="individuals" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-green-600" />
                  أفضل 10 في الحضور
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {reportData.topAttendees.slice(0, 10).map((user, index) => (
                    <div
                      key={user.id}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-sm font-bold">
                            {index + 1}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-sm text-gray-500">
                            السنة {user.year}
                          </div>
                        </div>
                      </div>
                      <Badge className="bg-green-100 text-green-700">
                        {user.percentage}%
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                  يحتاج متابعة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {reportData.frequentAbsentees.slice(0, 10).map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center">
                          <AlertTriangle className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-sm text-gray-500">
                            السنة {user.year}
                          </div>
                        </div>
                      </div>
                      <Badge variant="destructive">{user.percentage}%</Badge>
                    </div>
                  ))}
                  {reportData.frequentAbsentees.length === 0 && (
                    <div className="text-center py-4 text-gray-500">
                      جميع الأعضاء لديهم حضور جيد! 🎉
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
