"use client"

import { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar, Gift, MessageSquare, Download, Users, Cake, Phone, ExternalLink } from "lucide-react"
import { useAppStore } from "@/stores/app-store"
import { useRTL } from "@/contexts/rtl-context"
import { cn } from "@/lib/utils"
import { format, getMonth, getDate, isToday, isTomorrow } from "date-fns"
import { ar } from "date-fns/locale"

export default function BirthdaysPage() {
  const { users, settings } = useAppStore()
  const { isRTL, direction } = useRTL()
  const [selectedMonth, setSelectedMonth] = useState((new Date().getMonth() + 1).toString())

  const months = [
    { value: "1", label: "يناير" },
    { value: "2", label: "فبراير" },
    { value: "3", label: "مارس" },
    { value: "4", label: "أبريل" },
    { value: "5", label: "مايو" },
    { value: "6", label: "يونيو" },
    { value: "7", label: "يوليو" },
    { value: "8", label: "أغسطس" },
    { value: "9", label: "سبتمبر" },
    { value: "10", label: "أكتوبر" },
    { value: "11", label: "نوفمبر" },
    { value: "12", label: "ديسمبر" },
  ]

  // Filter users by selected month
  const birthdayUsers = useMemo(() => {
    return users
      .filter((user) => {
        const birthMonth = getMonth(new Date(user.birthdate)) + 1
        return selectedMonth === "all" || birthMonth.toString() === selectedMonth
      })
      .sort((a, b) => {
        const dayA = getDate(new Date(a.birthdate))
        const dayB = getDate(new Date(b.birthdate))
        return dayA - dayB
      })
  }, [users, selectedMonth])

  // Get upcoming birthdays (next 7 days)
  const upcomingBirthdays = useMemo(() => {
    const today = new Date()
    return users
      .filter((user) => {
        const birthDate = new Date(user.birthdate)
        const thisYearBirthday = new Date(today.getFullYear(), birthDate.getMonth(), birthDate.getDate())

        if (thisYearBirthday < today) {
          thisYearBirthday.setFullYear(today.getFullYear() + 1)
        }

        const diffTime = thisYearBirthday.getTime() - today.getTime()
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

        return diffDays <= 7 && diffDays >= 0
      })
      .sort((a, b) => {
        const today = new Date()
        const birthA = new Date(today.getFullYear(), new Date(a.birthdate).getMonth(), new Date(a.birthdate).getDate())
        const birthB = new Date(today.getFullYear(), new Date(b.birthdate).getMonth(), new Date(b.birthdate).getDate())

        if (birthA < today) birthA.setFullYear(today.getFullYear() + 1)
        if (birthB < today) birthB.setFullYear(today.getFullYear() + 1)

        return birthA.getTime() - birthB.getTime()
      })
  }, [users])

  const sendWhatsAppMessage = (user: any) => {
    const message = settings.whatsapp_message_template.replace("{{name}}", user.name)
    const encodedMessage = encodeURIComponent(message)
    const whatsappUrl = `https://wa.me/${user.phone.replace(/\D/g, "")}?text=${encodedMessage}`
    window.open(whatsappUrl, "_blank")
  }

  const exportBirthdays = () => {
    const csvContent = [
      ["الاسم", "رقم الهاتف", "تاريخ الميلاد", "السنة"].join(","),
      ...birthdayUsers.map((user) =>
        [user.name, user.phone, format(new Date(user.birthdate), "dd/MM/yyyy"), user.year].join(","),
      ),
    ].join("\n")

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const link = document.createElement("a")
    link.href = URL.createObjectURL(blob)
    link.download = `birthdays_${selectedMonth === "all" ? "all" : months.find((m) => m.value === selectedMonth)?.label}.csv`
    link.click()
  }

  const getBirthdayStatus = (birthdate: string) => {
    const today = new Date()
    const birthDate = new Date(birthdate)
    const thisYearBirthday = new Date(today.getFullYear(), birthDate.getMonth(), birthDate.getDate())

    if (isToday(thisYearBirthday)) {
      return { status: "today", label: "اليوم", color: "bg-green-100 text-green-700" }
    } else if (isTomorrow(thisYearBirthday)) {
      return { status: "tomorrow", label: "غداً", color: "bg-blue-100 text-blue-700" }
    } else {
      const diffTime = thisYearBirthday.getTime() - today.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays > 0 && diffDays <= 7) {
        return { status: "upcoming", label: `خلال ${diffDays} أيام`, color: "bg-yellow-100 text-yellow-700" }
      }
    }

    return null
  }

  return (
    <div className="p-6 space-y-6 page-transition font-cairo" dir={direction}>
      {/* Header */}
      <div className="animate-fade-in">
        <div className={cn("flex items-center gap-3 mb-2", isRTL ? "flex-row-reverse" : "flex-row")}>
          <div className="p-2 bg-gradient-to-r from-pink-500 to-rose-500 rounded-lg">
            <Calendar className="h-6 w-6 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-heading gradient-text">أعياد الميلاد</h1>
          <Gift className="h-6 w-6 text-pink-500 animate-bounce-gentle" />
        </div>
        <p className={cn("text-gray-600 text-lg text-body", isRTL ? "text-right" : "text-left")}>
          إدارة أعياد ميلاد الأعضاء وإرسال التهاني
        </p>
      </div>

      {/* Upcoming Birthdays Alert */}
      {upcomingBirthdays.length > 0 && (
        <Card className="animate-scale-in border-pink-200 bg-gradient-to-r from-pink-50 to-rose-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-pink-800">
              <Cake className="h-5 w-5" />
              أعياد ميلاد قريبة! 🎉
            </CardTitle>
            <CardDescription className="text-pink-600">
              {upcomingBirthdays.length} عضو لديهم أعياد ميلاد في الأسبوع القادم
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {upcomingBirthdays.slice(0, 6).map((user) => {
                const status = getBirthdayStatus(user.birthdate)
                return (
                  <div key={user.id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center">
                        <Cake className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-gray-500">
                          {format(new Date(user.birthdate), "dd MMMM", { locale: ar })}
                        </div>
                      </div>
                    </div>
                    {status && <Badge className={status.color}>{status.label}</Badge>}
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters and Actions */}
      <Card className="animate-scale-in">
        <CardHeader>
          <div className={cn("flex items-center justify-between", isRTL ? "flex-row-reverse" : "flex-row")}>
            <div>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                تصفية أعياد الميلاد
              </CardTitle>
              <CardDescription>اختر الشهر لعرض أعياد الميلاد</CardDescription>
            </div>
            <Button onClick={exportBirthdays} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              تصدير القائمة
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Select value={selectedMonth} onValueChange={setSelectedMonth}>
              <SelectTrigger>
                <SelectValue placeholder="اختر الشهر" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الشهور</SelectItem>
                {months.map((month) => (
                  <SelectItem key={month.value} value={month.value}>
                    {month.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Users className="h-4 w-4" />
              <span>{birthdayUsers.length} عضو</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Birthday List */}
      <Card className="animate-slide-in-right">
        <CardHeader>
          <CardTitle>
            قائمة أعياد الميلاد -{" "}
            {selectedMonth === "all" ? "جميع الشهور" : months.find((m) => m.value === selectedMonth)?.label}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {birthdayUsers.length > 0 ? (
            <div className="space-y-3">
              {birthdayUsers.map((user) => {
                const status = getBirthdayStatus(user.birthdate)
                return (
                  <div
                    key={user.id}
                    className={cn(
                      "flex items-center justify-between p-4 border rounded-lg transition-colors hover:bg-gray-50",
                      status?.status === "today" && "bg-green-50 border-green-200",
                      status?.status === "tomorrow" && "bg-blue-50 border-blue-200",
                    )}
                  >
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold text-lg">{getDate(new Date(user.birthdate))}</span>
                      </div>
                      <div>
                        <div className="font-medium text-lg">{user.name}</div>
                        <div className="text-sm text-gray-500 flex items-center gap-4">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {format(new Date(user.birthdate), "dd MMMM yyyy", { locale: ar })}
                          </span>
                          <span className="flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            {user.phone}
                          </span>
                          <span>السنة {user.year}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      {status && <Badge className={status.color}>{status.label}</Badge>}
                      <Button
                        onClick={() => sendWhatsAppMessage(user)}
                        className="bg-green-600 hover:bg-green-700 text-white"
                        size="sm"
                      >
                        <MessageSquare className="h-4 w-4 mr-2" />
                        واتساب
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </Button>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <Cake className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد أعياد ميلاد</h3>
              <p className="text-gray-500">
                {selectedMonth === "all"
                  ? "لا توجد أعياد ميلاد مسجلة في النظام"
                  : `لا توجد أعياد ميلاد في شهر ${months.find((m) => m.value === selectedMonth)?.label}`}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
