"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Users, Eye, EyeOff, Sparkles, Shield, Clock } from "lucide-react"

export default function LoginPage() {
  const [email, setEmail] = useState("<EMAIL>")
  const [password, setPassword] = useState("password")
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState("")
  const [loading, setLoading] = useState(false)
  const [mounted, setMounted] = useState(false)
  const { signIn } = useAuth()
  const router = useRouter()

  useEffect(() => {
    setMounted(true)
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    // Simulate authentication delay for better UX
    await new Promise((resolve) => setTimeout(resolve, 800))

    const { error } = await signIn(email, password)

    if (error) {
      setError("بيانات الدخول غير صحيحة. يرجى المحاولة مرة أخرى.")
    } else {
      router.push("/dashboard")
    }

    setLoading(false)
  }

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <div className="animate-pulse-soft">
          <Users className="h-12 w-12 text-blue-600" />
        </div>
      </div>
    )
  }

  return (
    <div
      className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4 relative overflow-hidden font-cairo"
      dir="rtl"
    >
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-soft"></div>
        <div
          className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-soft"
          style={{ animationDelay: "2s" }}
        ></div>
        <div
          className="absolute top-40 left-40 w-80 h-80 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-soft"
          style={{ animationDelay: "4s" }}
        ></div>
      </div>

      {/* Main Login Card */}
      <Card className="w-full max-w-md relative z-10 animate-scale-in shadow-large border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="text-center pb-8">
          {/* Animated Logo */}
          <div className="flex justify-center mb-6">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-lg opacity-30 animate-pulse-soft"></div>
              <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-4 rounded-full hover-lift">
                <Users className="h-10 w-10 text-white" />
              </div>
            </div>
          </div>

          {/* Title with Gradient */}
          <CardTitle className="text-3xl font-bold text-heading gradient-text mb-2">نظام حضور الشباب</CardTitle>
          <CardDescription className="text-gray-600 text-lg text-body">مرحباً بك في نظام إدارة الحضور</CardDescription>

          {/* Feature Pills */}
          <div className="flex flex-wrap justify-center gap-2 mt-4">
            <div className="flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm animate-slide-in-right">
              <Shield className="h-3 w-3" />
              <span className="font-cairo font-medium">آمن</span>
            </div>
            <div
              className="flex items-center gap-1 px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm animate-slide-in-right"
              style={{ animationDelay: "0.1s" }}
            >
              <Clock className="h-3 w-3" />
              <span className="font-cairo font-medium">سريع</span>
            </div>
            <div
              className="flex items-center gap-1 px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm animate-slide-in-right"
              style={{ animationDelay: "0.2s" }}
            >
              <Sparkles className="h-3 w-3" />
              <span className="font-cairo font-medium">سهل الاستخدام</span>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Input */}
            <div className="space-y-2 animate-slide-in-left" style={{ animationDelay: "0.3s" }}>
              <Label htmlFor="email" className="text-sm font-medium text-gray-700 font-cairo">
                البريد الإلكتروني
              </Label>
              <div className="relative">
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  required
                  disabled={loading}
                  className="pl-4 pr-4 h-12 border-2 border-gray-200 focus:border-blue-500 transition-all duration-300 hover:border-gray-300 font-cairo focus-ring"
                />
              </div>
            </div>

            {/* Password Input */}
            <div className="space-y-2 animate-slide-in-left" style={{ animationDelay: "0.4s" }}>
              <Label htmlFor="password" className="text-sm font-medium text-gray-700 font-cairo">
                كلمة المرور
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="••••••••"
                  required
                  disabled={loading}
                  className="pl-12 pr-4 h-12 border-2 border-gray-200 focus:border-blue-500 transition-all duration-300 hover:border-gray-300 font-cairo focus-ring"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors focus-ring rounded"
                  disabled={loading}
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <Alert variant="destructive" className="animate-scale-in">
                <AlertDescription className="text-center font-cairo">{error}</AlertDescription>
              </Alert>
            )}

            {/* Login Button */}
            <Button
              type="submit"
              className="w-full h-12 btn-gradient text-white font-medium rounded-lg transition-all duration-300 hover-lift animate-slide-in-left font-cairo focus-ring"
              style={{ animationDelay: "0.5s" }}
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  <span className="font-cairo">جاري تسجيل الدخول...</span>
                </>
              ) : (
                <>
                  <span className="font-cairo font-medium">تسجيل الدخول</span>
                  <div className="mr-2 w-5 h-5 bg-white/20 rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </>
              )}
            </Button>

            {/* Demo Credentials Info */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg border animate-fade-in" style={{ animationDelay: "0.6s" }}>
              <p className="text-sm text-gray-600 text-center mb-2 font-medium font-cairo">بيانات الدخول التجريبية:</p>
              <div className="space-y-1 text-xs text-gray-500 font-cairo">
                <p>
                  <span className="font-medium">البريد:</span> <EMAIL>
                </p>
                <p>
                  <span className="font-medium">كلمة المرور:</span> password
                </p>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Footer */}
      <div
        className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-center animate-fade-in"
        style={{ animationDelay: "0.8s" }}
      >
        <p className="text-sm text-gray-500 font-cairo">© 2024 نظام حضور الشباب. جميع الحقوق محفوظة.</p>
      </div>
    </div>
  )
}
