"use client"

import { useState, useMemo } from "react"
import { use<PERSON>arams } from "next/navigation"
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  flexRender,
  type ColumnDef,
  type SortingState,
  type ColumnFiltersState,
} from "@tanstack/react-table"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Search,
  Filter,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  QrCode,
  Calendar,
  GraduationCap,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  Download,
  Upload,
  Users,
  CheckCircle,
  X,
  Phone,
  MapPin,
  Facebook,
  Eye,
  UserCheck,
} from "lucide-react"
import { useAppStore, type User as AppUser } from "@/stores/app-store"
import { useRTL } from "@/contexts/rtl-context"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { ar } from "date-fns/locale"

export default function UsersPage() {
  const params = useParams()
  const year = Number.parseInt(params.year as string) as 1 | 2 | 3 | 4
  const { users, deleteUser, setSelectedUser, exportUsers, bulkMarkAttendance } = useAppStore()
  const { isRTL, direction } = useRTL()

  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [globalFilter, setGlobalFilter] = useState("")
  const [showQRModal, setShowQRModal] = useState<AppUser | null>(null)
  const [showUserDetails, setShowUserDetails] = useState<AppUser | null>(null)
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)

  // Filter users by year
  const yearUsers = useMemo(() => users.filter((user) => user.year === year), [users, year])

  const columns: ColumnDef<AppUser>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => {
            table.toggleAllPageRowsSelected(!!value)
            if (value) {
              setSelectedUsers(table.getRowModel().rows.map((row) => row.original.id))
            } else {
              setSelectedUsers([])
            }
          }}
          aria-label="تحديد الكل"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={selectedUsers.includes(row.original.id)}
          onCheckedChange={(value) => {
            if (value) {
              setSelectedUsers([...selectedUsers, row.original.id])
            } else {
              setSelectedUsers(selectedUsers.filter((id) => id !== row.original.id))
            }
          }}
          aria-label="تحديد الصف"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium"
        >
          الاسم
          <ArrowUpDown className="mr-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-bold">{row.original.name.charAt(0)}</span>
          </div>
          <div>
            <div className="font-medium">{row.getValue("name")}</div>
            <div className="text-sm text-gray-500 flex items-center gap-2">
              <Phone className="h-3 w-3" />
              {row.original.phone}
            </div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: "gender",
      header: "النوع",
      cell: ({ row }) => (
        <Badge variant={row.getValue("gender") === "male" ? "default" : "secondary"}>
          {row.getValue("gender") === "male" ? "ذكر" : "أنثى"}
        </Badge>
      ),
    },
    {
      accessorKey: "college",
      header: "الكلية",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <GraduationCap className="h-4 w-4 text-gray-500" />
          <div>
            <div className="font-medium">{row.getValue("college")}</div>
            <div className="text-sm text-gray-500">{row.original.department}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: "birthdate",
      header: "تاريخ الميلاد",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-gray-500" />
          <span>{format(new Date(row.getValue("birthdate")), "dd/MM/yyyy", { locale: ar })}</span>
        </div>
      ),
    },
    {
      accessorKey: "first_attendance_date",
      header: "أول حضور",
      cell: ({ row }) => (
        <span>{format(new Date(row.getValue("first_attendance_date")), "dd/MM/yyyy", { locale: ar })}</span>
      ),
    },
    {
      id: "actions",
      header: "الإجراءات",
      cell: ({ row }) => {
        const user = row.original

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setShowUserDetails(user)}>
                <Eye className="mr-2 h-4 w-4" />
                عرض التفاصيل
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setShowQRModal(user)}>
                <QrCode className="mr-2 h-4 w-4" />
                عرض QR Code
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSelectedUser(user)}>
                <Edit className="mr-2 h-4 w-4" />
                تعديل
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => bulkMarkAttendance([user.id], true, "<EMAIL>")}
                className="text-green-600"
              >
                <UserCheck className="mr-2 h-4 w-4" />
                تسجيل حضور
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => deleteUser(user.id)} className="text-red-600">
                <Trash2 className="mr-2 h-4 w-4" />
                حذف
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  const table = useReactTable({
    data: yearUsers,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
      globalFilter,
    },
  })

  const yearNames = {
    1: "السنة الأولى",
    2: "السنة الثانية",
    3: "السنة الثالثة",
    4: "السنة الرابعة",
  }

  const handleExport = (format: "csv" | "excel" | "pdf") => {
    exportUsers(format, { year })
  }

  const handleBulkAction = (action: string) => {
    if (action === "mark_present") {
      bulkMarkAttendance(selectedUsers, true, "<EMAIL>")
    } else if (action === "mark_absent") {
      bulkMarkAttendance(selectedUsers, false, "<EMAIL>")
    }
    setSelectedUsers([])
    setShowBulkActions(false)
  }

  return (
    <div className="p-6 space-y-6 page-transition font-cairo" dir={direction}>
      {/* Header */}
      <div className="animate-fade-in">
        <div className={cn("flex items-center justify-between", isRTL ? "flex-row-reverse" : "flex-row")}>
          <div>
            <div className={cn("flex items-center gap-3 mb-2", isRTL ? "flex-row-reverse" : "flex-row")}>
              <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg">
                <Users className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-heading gradient-text">{yearNames[year]}</h1>
              <Badge variant="outline" className="text-lg px-3 py-1">
                {yearUsers.length} طالب
              </Badge>
            </div>
            <p className={cn("text-gray-600 text-lg text-body", isRTL ? "text-right" : "text-left")}>
              إدارة طلاب {yearNames[year]} - عرض وتعديل بيانات الطلاب
            </p>
          </div>

          <div className="flex gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  تصدير
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleExport("csv")}>تصدير CSV</DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport("excel")}>تصدير Excel</DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport("pdf")}>تصدير PDF</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              استيراد
            </Button>

            <Button className="btn-gradient">
              <Plus className="h-4 w-4 mr-2" />
              إضافة طالب جديد
            </Button>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedUsers.length > 0 && (
        <Card className="animate-scale-in border-blue-200 bg-blue-50">
          <CardContent className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-blue-600" />
              <span className="font-medium text-blue-800">تم تحديد {selectedUsers.length} طالب</span>
            </div>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                onClick={() => handleBulkAction("mark_present")}
                className="bg-green-600 hover:bg-green-700"
              >
                <UserCheck className="h-4 w-4 mr-2" />
                تسجيل حضور
              </Button>
              <Button size="sm" variant="outline" onClick={() => handleBulkAction("mark_absent")}>
                تسجيل غياب
              </Button>
              <Button size="sm" variant="ghost" onClick={() => setSelectedUsers([])}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Filters */}
      <Card className="animate-scale-in">
        <CardHeader>
          <div className={cn("flex items-center justify-between", isRTL ? "flex-row-reverse" : "flex-row")}>
            <div>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                البحث والتصفية المتقدمة
              </CardTitle>
              <CardDescription>ابحث وصفي قائمة الطلاب بطرق متعددة</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative md:col-span-2">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="البحث بالاسم، الهاتف، أو الكلية..."
                value={globalFilter ?? ""}
                onChange={(e) => setGlobalFilter(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select
              value={(table.getColumn("gender")?.getFilterValue() as string) ?? ""}
              onValueChange={(value) => table.getColumn("gender")?.setFilterValue(value === "all" ? "" : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="النوع" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">الكل</SelectItem>
                <SelectItem value="male">ذكر</SelectItem>
                <SelectItem value="female">أنثى</SelectItem>
              </SelectContent>
            </Select>
            <Input
              placeholder="الكلية"
              value={(table.getColumn("college")?.getFilterValue() as string) ?? ""}
              onChange={(e) => table.getColumn("college")?.setFilterValue(e.target.value)}
            />
            <Select
              value={table.getState().pagination.pageSize.toString()}
              onValueChange={(value) => table.setPageSize(Number(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="عدد الصفوف" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10 صفوف</SelectItem>
                <SelectItem value="20">20 صف</SelectItem>
                <SelectItem value="50">50 صف</SelectItem>
                <SelectItem value="100">100 صف</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Data Table */}
      <Card className="animate-slide-in-right">
        <CardContent className="p-0">
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id} className="bg-gray-50">
                    {headerGroup.headers.map((header) => (
                      <TableHead key={header.id} className="text-right font-semibold">
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="hover:bg-gray-50 transition-colors"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="text-right">
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      <div className="flex flex-col items-center gap-2">
                        <Users className="h-8 w-8 text-gray-400" />
                        <span className="text-gray-500">لا توجد بيانات</span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Enhanced Pagination */}
          <div className="flex items-center justify-between px-6 py-4 border-t">
            <div className="text-sm text-gray-500">
              عرض {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} إلى{" "}
              {Math.min(
                (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
                table.getFilteredRowModel().rows.length,
              )}{" "}
              من {table.getFilteredRowModel().rows.length} نتيجة
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
              >
                الأول
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <ChevronRight className="h-4 w-4" />
                السابق
              </Button>
              <span className="text-sm text-gray-600">
                صفحة {table.getState().pagination.pageIndex + 1} من {table.getPageCount()}
              </span>
              <Button variant="outline" size="sm" onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>
                التالي
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
              >
                الأخير
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User Details Modal */}
      {showUserDetails && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-2xl animate-scale-in max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  تفاصيل العضو - {showUserDetails.name}
                </CardTitle>
                <Button variant="ghost" size="icon" onClick={() => setShowUserDetails(null)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">الاسم الكامل</label>
                    <p className="text-lg font-medium">{showUserDetails.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">رقم الهاتف</label>
                    <p className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      {showUserDetails.phone}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">النوع</label>
                    <p>
                      <Badge variant={showUserDetails.gender === "male" ? "default" : "secondary"}>
                        {showUserDetails.gender === "male" ? "ذكر" : "أنثى"}
                      </Badge>
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">السنة الدراسية</label>
                    <p className="text-lg font-medium">السنة {showUserDetails.year}</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">الكلية</label>
                    <p className="flex items-center gap-2">
                      <GraduationCap className="h-4 w-4" />
                      {showUserDetails.college}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">القسم</label>
                    <p>{showUserDetails.department}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">تاريخ الميلاد</label>
                    <p className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      {format(new Date(showUserDetails.birthdate), "dd MMMM yyyy", { locale: ar })}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">أول حضور</label>
                    <p>{format(new Date(showUserDetails.first_attendance_date), "dd MMMM yyyy", { locale: ar })}</p>
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">العنوان</label>
                <p className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  {showUserDetails.address}
                </p>
              </div>

              {showUserDetails.facebook_url && (
                <div>
                  <label className="text-sm font-medium text-gray-500">الفيسبوك</label>
                  <p className="flex items-center gap-2">
                    <Facebook className="h-4 w-4" />
                    <a
                      href={showUserDetails.facebook_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {showUserDetails.facebook_url}
                    </a>
                  </p>
                </div>
              )}

              <div className="flex gap-2 pt-4 border-t">
                <Button onClick={() => setShowQRModal(showUserDetails)} variant="outline" className="flex-1">
                  <QrCode className="h-4 w-4 mr-2" />
                  عرض QR Code
                </Button>
                <Button onClick={() => setSelectedUser(showUserDetails)} className="flex-1 btn-gradient">
                  <Edit className="h-4 w-4 mr-2" />
                  تعديل البيانات
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Enhanced QR Code Modal */}
      {showQRModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md animate-scale-in">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <QrCode className="h-5 w-5" />
                  QR Code - {showQRModal.name}
                </CardTitle>
                <Button variant="ghost" size="icon" onClick={() => setShowQRModal(null)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <div className="w-48 h-48 bg-gray-100 rounded-lg mx-auto flex items-center justify-center">
                <QrCode className="h-24 w-24 text-gray-400" />
              </div>
              <div>
                <p className="font-medium">{showQRModal.name}</p>
                <p className="text-sm text-gray-500">
                  السنة {showQRModal.year} - {showQRModal.phone}
                </p>
              </div>
              <p className="text-sm text-gray-600">امسح هذا الرمز لتسجيل الحضور</p>
              <div className="flex gap-2">
                <Button variant="outline" className="flex-1 bg-transparent">
                  <Download className="h-4 w-4 mr-2" />
                  تحميل
                </Button>
                <Button variant="outline" className="flex-1 bg-transparent">
                  طباعة
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
