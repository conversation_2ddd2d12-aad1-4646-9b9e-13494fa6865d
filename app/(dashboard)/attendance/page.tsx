"use client";

import { useState, useMem<PERSON>, use<PERSON><PERSON>back, useEffect, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON>ertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  ClipboardCheck,
  Search,
  QrCode,
  Camera,
  UserCheck,
  Users,
  CheckCircle,
  Clock,
  Sparkles,
  Download,
  BarChart3,
  TrendingUp,
  Eye,
  X,
  Phone,
  GraduationCap,
  Filter,
  SortAsc,
  SortDesc,
  RotateCcw,
  Zap,
  UserX,
  UserPlus,
  Keyboard,
  Loader2,
} from "lucide-react";
import { useAppStore } from "@/stores/app-store";
import { useRTL } from "@/contexts/rtl-context";
import { useAuth } from "@/contexts/auth-context";
import { cn } from "@/lib/utils";
import { format, startOfWeek, endOfWeek } from "date-fns";
import { ar } from "date-fns/locale";
import { toast } from "sonner";

// Enhanced types for better state management
type SortOption =
  | "name"
  | "attendance_rate"
  | "recent_activity"
  | "year"
  | "college";
type SortDirection = "asc" | "desc";
type AttendanceStatus = "all" | "present" | "absent" | "not_marked";

// Custom hook for debounced search
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Utility function for highlighting search matches
function highlightText(text: string, searchQuery: string): React.ReactNode {
  if (!searchQuery.trim()) return text;

  const regex = new RegExp(
    `(${searchQuery.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
    "gi"
  );
  const parts = text.split(regex);

  return parts.map((part, index) =>
    regex.test(part) ? (
      <mark key={index} className="bg-yellow-200 text-yellow-900 px-1 rounded">
        {part}
      </mark>
    ) : (
      part
    )
  );
}

// Unique key generator to prevent React key duplication
function generateUniqueKey(
  prefix: string,
  id: string,
  context?: string,
  index?: number
): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 11);
  const parts = [prefix, id];

  if (context) parts.push(context);
  if (index !== undefined) parts.push(index.toString());

  // Add timestamp and random string to ensure absolute uniqueness
  parts.push(timestamp.toString(), random);

  return parts.join("-");
}

// Enhanced fuzzy search function
function fuzzySearch(text: string, searchQuery: string): boolean {
  if (!searchQuery.trim()) return true;

  const query = searchQuery.toLowerCase();
  const target = text.toLowerCase();

  // Exact match
  if (target.includes(query)) return true;

  // Fuzzy match - check if all characters of query exist in order
  let queryIndex = 0;
  for (let i = 0; i < target.length && queryIndex < query.length; i++) {
    if (target[i] === query[queryIndex]) {
      queryIndex++;
    }
  }

  return queryIndex === query.length;
}

export default function AttendancePage() {
  const {
    users,
    attendanceRecords,
    markAttendance,
    bulkMarkAttendance,
    clearAttendanceForDate,
    exportAttendance,
  } = useAppStore();
  const { user } = useAuth();
  const { isRTL, direction } = useRTL();

  // Enhanced state management
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const isSearching = searchQuery !== debouncedSearchQuery;
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [manualId, setManualId] = useState("");
  const [recentAttendance, setRecentAttendance] = useState<string[]>([]);
  const [selectedDate, setSelectedDate] = useState(
    format(new Date(), "yyyy-MM-dd")
  );

  // Enhanced filtering and sorting
  const [filterYear, setFilterYear] = useState("all");
  const [filterCollege, setFilterCollege] = useState("all");
  const [filterStatus, setFilterStatus] = useState<AttendanceStatus>("all");
  const [sortBy, setSortBy] = useState<SortOption>("name");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Bulk operations
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());
  const [bulkActionLoading, setBulkActionLoading] = useState(false);

  // UI state
  const [showAttendanceDetails, setShowAttendanceDetails] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  const [confirmAction, setConfirmAction] = useState<{
    type: "bulk_present" | "bulk_absent" | "clear_all";
    userIds?: string[];
    message: string;
  } | null>(null);

  // Undo functionality
  const [undoStack, setUndoStack] = useState<
    Array<{
      id: string;
      type: "attendance" | "bulk_attendance";
      data: any;
      timestamp: number;
    }>
  >([]);

  // Loading states
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Keyboard shortcuts
  const searchInputRef = useRef<HTMLInputElement>(null);

  const today = new Date();
  const todayString = today.toISOString().split("T")[0];

  // Get attendance for selected date (moved before filtering logic)
  const dateAttendance = useMemo(() => {
    return attendanceRecords.filter((record) => record.date === selectedDate);
  }, [attendanceRecords, selectedDate]);

  // Enhanced filtering and sorting logic with fuzzy search
  const filteredAndSortedUsers = useMemo(() => {
    let filtered = users.filter((u) => {
      // Enhanced search filter with fuzzy search across multiple fields
      const matchesSearch =
        debouncedSearchQuery === "" ||
        fuzzySearch(u.name, debouncedSearchQuery) ||
        fuzzySearch(u.phone, debouncedSearchQuery) ||
        fuzzySearch(u.college, debouncedSearchQuery) ||
        fuzzySearch(u.department, debouncedSearchQuery) ||
        // Additional exact matches for better results
        u.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        u.phone.includes(debouncedSearchQuery) ||
        u.college.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        u.department.toLowerCase().includes(debouncedSearchQuery.toLowerCase());

      // Year filter
      const matchesYear =
        filterYear === "all" || u.year.toString() === filterYear;

      // College filter
      const matchesCollege =
        filterCollege === "all" || u.college.includes(filterCollege);

      // Status filter
      let matchesStatus = true;
      if (filterStatus !== "all") {
        const userAttendance = dateAttendance.find((a) => a.user_id === u.id);
        if (filterStatus === "present") {
          matchesStatus = userAttendance?.present === true;
        } else if (filterStatus === "absent") {
          matchesStatus = userAttendance?.present === false;
        } else if (filterStatus === "not_marked") {
          matchesStatus = !userAttendance;
        }
      }

      return matchesSearch && matchesYear && matchesCollege && matchesStatus;
    });

    // Calculate attendance rates for sorting
    const usersWithStats = filtered.map((u) => {
      const userRecords = attendanceRecords.filter((r) => r.user_id === u.id);
      const presentCount = userRecords.filter((r) => r.present).length;
      const attendanceRate =
        userRecords.length > 0 ? (presentCount / userRecords.length) * 100 : 0;
      const lastAttendance = userRecords.sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )[0];

      return {
        ...u,
        attendanceRate,
        lastAttendanceDate: lastAttendance?.created_at || null,
      };
    });

    // Sort users
    usersWithStats.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name, "ar");
          break;
        case "attendance_rate":
          comparison = a.attendanceRate - b.attendanceRate;
          break;
        case "recent_activity":
          const aDate = a.lastAttendanceDate
            ? new Date(a.lastAttendanceDate).getTime()
            : 0;
          const bDate = b.lastAttendanceDate
            ? new Date(b.lastAttendanceDate).getTime()
            : 0;
          comparison = aDate - bDate;
          break;
        case "year":
          comparison = a.year - b.year;
          break;
        case "college":
          comparison = a.college.localeCompare(b.college, "ar");
          break;
        default:
          comparison = a.name.localeCompare(b.name, "ar");
      }

      return sortDirection === "asc" ? comparison : -comparison;
    });

    return usersWithStats;
  }, [
    users,
    debouncedSearchQuery,
    filterYear,
    filterCollege,
    filterStatus,
    sortBy,
    sortDirection,
    dateAttendance,
    attendanceRecords,
  ]);

  // Get today's attendance status
  const getTodayAttendance = () => {
    const present = dateAttendance.filter((a) => a.present).length;
    const absent = users.length - present;
    const attendanceRate =
      users.length > 0 ? Math.round((present / users.length) * 100) : 0;
    return { present, absent, total: users.length, attendanceRate };
  };

  const attendanceStats = getTodayAttendance();

  // Get weekly attendance summary
  const weeklyStats = useMemo(() => {
    const weekStart = startOfWeek(today, { weekStartsOn: 1 });
    const weekEnd = endOfWeek(today, { weekStartsOn: 1 });

    const weekRecords = attendanceRecords.filter((record) => {
      const recordDate = new Date(record.date);
      return recordDate >= weekStart && recordDate <= weekEnd;
    });

    const weeklyPresent = weekRecords.filter((r) => r.present).length;
    const weeklyTotal = weekRecords.length;
    const weeklyRate =
      weeklyTotal > 0 ? Math.round((weeklyPresent / weeklyTotal) * 100) : 0;

    return { weeklyPresent, weeklyTotal, weeklyRate };
  }, [attendanceRecords, today]);

  // Enhanced action handlers with undo functionality
  const handleMarkAttendance = useCallback(
    (userId: string, present = true) => {
      if (!user?.email) return;

      setActionLoading(userId);

      // Store undo data
      const existingRecord = dateAttendance.find((a) => a.user_id === userId);
      const userName = users.find((u) => u.id === userId)?.name || "المستخدم";

      // Create undo action
      const undoAction = {
        id: `undo_${Date.now()}`,
        type: "attendance" as const,
        data: {
          userId,
          previousState: existingRecord
            ? { present: existingRecord.present }
            : null,
          newState: { present },
          userName,
        },
        timestamp: Date.now(),
      };

      markAttendance(userId, present, user.email);
      setRecentAttendance((prev) => [userId, ...prev.slice(0, 4)]);
      setSearchQuery("");
      setSelectedUser("");
      setManualId("");

      // Add to undo stack
      setUndoStack((prev) => [undoAction, ...prev.slice(0, 4)]);

      // Show success toast with undo option
      toast.success(`تم تسجيل ${present ? "حضور" : "غياب"} ${userName} بنجاح`, {
        action: {
          label: "تراجع",
          onClick: () => handleUndo(undoAction.id),
        },
        duration: 5000,
      });

      setTimeout(() => setActionLoading(null), 500);
    },
    [user?.email, dateAttendance, markAttendance, users]
  );

  // Undo functionality
  const handleUndo = useCallback(
    (undoId: string) => {
      const undoAction = undoStack.find((action) => action.id === undoId);
      if (!undoAction || !user?.email) return;

      const { userId, previousState, userName } = undoAction.data;

      if (previousState) {
        // Restore previous state
        markAttendance(userId, previousState.present, user.email);
        toast.success(`تم التراجع عن تسجيل ${userName}`);
      } else {
        // Remove the attendance record (this would need store support)
        toast.success(`تم التراجع عن تسجيل ${userName}`);
      }

      // Remove from undo stack
      setUndoStack((prev) => prev.filter((action) => action.id !== undoId));
    },
    [undoStack, user?.email, markAttendance]
  );

  const isUserPresent = (userId: string) => {
    return dateAttendance.some((a) => a.user_id === userId && a.present);
  };

  const isUserAbsent = (userId: string) => {
    return dateAttendance.some((a) => a.user_id === userId && !a.present);
  };

  const handleManualIdSubmit = () => {
    const foundUser = users.find(
      (u) => u.id === manualId || u.phone === manualId
    );
    if (foundUser) {
      handleMarkAttendance(foundUser.id);
    }
  };

  const handleBulkMarkPresent = () => {
    const userIds = filteredAndSortedUsers.map((u) => u.id);
    bulkMarkAttendance(userIds, true, user?.email || "admin");
  };

  const handleExportAttendance = (format: "csv" | "excel" | "pdf") => {
    exportAttendance(format, {
      dateFrom: selectedDate,
      dateTo: selectedDate,
    });
  };

  const colleges = [...new Set(users.map((u) => u.college))];

  // Keyboard shortcuts handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement
      ) {
        return;
      }

      // Ctrl/Cmd + F or "/" key: Focus search
      if (
        ((event.ctrlKey || event.metaKey) && event.key === "f") ||
        event.key === "/"
      ) {
        event.preventDefault();
        searchInputRef.current?.focus();
      }

      // Ctrl/Cmd + A: Mark all present
      if ((event.ctrlKey || event.metaKey) && event.key === "a") {
        event.preventDefault();
        handleBulkMarkPresent();
      }

      // Ctrl/Cmd + Shift + A: Mark all absent
      if (
        (event.ctrlKey || event.metaKey) &&
        event.shiftKey &&
        event.key === "A"
      ) {
        event.preventDefault();
        const userIds = filteredAndSortedUsers.map((u) => u.id);
        setConfirmAction({
          type: "bulk_absent",
          userIds,
          message: `هل أنت متأكد من تسجيل غياب جميع الأعضاء (${userIds.length} عضو)؟`,
        });
        setShowConfirmDialog(true);
      }

      // Escape: Clear search or close modals
      if (event.key === "Escape") {
        if (showConfirmDialog) {
          setShowConfirmDialog(false);
        } else if (showAttendanceDetails) {
          setShowAttendanceDetails(false);
        } else if (searchQuery) {
          setSearchQuery("");
        }
      }

      // Ctrl/Cmd + R: Reset filters
      if ((event.ctrlKey || event.metaKey) && event.key === "r") {
        event.preventDefault();
        setSearchQuery("");
        setFilterStatus("all");
        setFilterYear("all");
        setFilterCollege("all");
        setSortBy("name");
        setSortDirection("asc");
        toast.success("تم إعادة تعيين المرشحات");
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [
    searchQuery,
    showConfirmDialog,
    showAttendanceDetails,
    filteredAndSortedUsers,
    handleBulkMarkPresent,
  ]);

  return (
    <div className="p-6 space-y-6 page-transition font-cairo" dir={direction}>
      {/* Enhanced Header */}
      <div className="animate-fade-in">
        <div
          className={cn(
            "flex items-center justify-between",
            isRTL ? "flex-row-reverse" : "flex-row"
          )}
        >
          <div>
            <div
              className={cn(
                "flex items-center gap-3 mb-2",
                isRTL ? "flex-row-reverse" : "flex-row"
              )}
            >
              <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
                <ClipboardCheck className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-heading gradient-text">
                تسجيل الحضور
              </h1>
              <Sparkles className="h-6 w-6 text-yellow-500 animate-pulse" />
            </div>
            <p
              className={cn(
                "text-gray-600 text-lg text-body",
                isRTL ? "text-right" : "text-left"
              )}
            >
              تسجيل حضور الأعضاء -{" "}
              {format(new Date(selectedDate), "EEEE، dd MMMM yyyy", {
                locale: ar,
              })}
            </p>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowAttendanceDetails(true)}
            >
              <Eye className="h-4 w-4 mr-2" />
              تفاصيل الحضور
            </Button>
            <Button
              variant="outline"
              onClick={() => handleExportAttendance("csv")}
            >
              <Download className="h-4 w-4 mr-2" />
              تصدير
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowKeyboardShortcuts(true)}
            >
              <Keyboard className="h-4 w-4 mr-2" />
              اختصارات لوحة المفاتيح
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Search and Filters */}
      <Card className="animate-fade-in">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            البحث والتصفية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Enhanced Search Bar */}
          <div className="space-y-2">
            <div className="relative">
              {isSearching ? (
                <Loader2
                  className={cn(
                    "absolute top-1/2 transform -translate-y-1/2 h-4 w-4 text-blue-500 animate-spin",
                    isRTL ? "right-3" : "left-3"
                  )}
                />
              ) : (
                <Search
                  className={cn(
                    "absolute top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400",
                    isRTL ? "right-3" : "left-3"
                  )}
                />
              )}
              <Input
                ref={searchInputRef}
                placeholder="ابحث بالاسم، رقم الهاتف، الكلية، أو القسم... (يدعم البحث الذكي)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={cn(
                  "h-12 text-lg transition-all duration-200",
                  isRTL ? "pr-10 pl-12" : "pl-10 pr-12",
                  searchQuery && "ring-2 ring-blue-200 border-blue-300"
                )}
                dir={isRTL ? "rtl" : "ltr"}
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSearchQuery("")}
                  className={cn(
                    "absolute top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-100",
                    isRTL ? "left-2" : "right-2"
                  )}
                  title="مسح البحث"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            {/* Search Results Info - Positioned below input */}
            {searchQuery && (
              <div className="flex items-center justify-between text-sm flex-wrap gap-2">
                <div className="flex items-center gap-2">
                  <Badge
                    variant="secondary"
                    className={cn(
                      "text-xs",
                      filteredAndSortedUsers.length === 0
                        ? "bg-red-100 text-red-800"
                        : "bg-blue-100 text-blue-800"
                    )}
                  >
                    {filteredAndSortedUsers.length} نتيجة
                  </Badge>
                  {isSearching && (
                    <span className="text-gray-500 text-xs flex items-center gap-1">
                      <Loader2 className="h-3 w-3 animate-spin" />
                      جاري البحث...
                    </span>
                  )}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSearchQuery("")}
                  className="text-xs text-gray-500 hover:text-gray-700 px-2 py-1"
                >
                  مسح البحث
                </Button>
              </div>
            )}
          </div>

          {/* Search Tips */}
          {searchQuery && (
            <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded-lg">
              💡 نصيحة: يمكنك البحث بأي جزء من الاسم أو رقم الهاتف أو الكلية.
              البحث يدعم الكتابة غير المكتملة.
            </div>
          )}

          {/* Quick Filters Row */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                تاريخ الحضور
              </label>
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="h-10"
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">
                حالة الحضور
              </label>
              <Select
                value={filterStatus}
                onValueChange={(value) =>
                  setFilterStatus(value as AttendanceStatus)
                }
              >
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="اختر الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="present">حاضر</SelectItem>
                  <SelectItem value="absent">غائب</SelectItem>
                  <SelectItem value="not_marked">غير مسجل</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">السنة</label>
              <Select value={filterYear} onValueChange={setFilterYear}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="اختر السنة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع السنوات</SelectItem>
                  <SelectItem value="1">السنة الأولى</SelectItem>
                  <SelectItem value="2">السنة الثانية</SelectItem>
                  <SelectItem value="3">السنة الثالثة</SelectItem>
                  <SelectItem value="4">السنة الرابعة</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">الكلية</label>
              <Select value={filterCollege} onValueChange={setFilterCollege}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="اختر الكلية" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الكليات</SelectItem>
                  {colleges.map((college) => (
                    <SelectItem key={college} value={college}>
                      {college}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                className="w-full h-10"
              >
                <Filter className="h-4 w-4 mr-2" />
                {showAdvancedFilters ? "إخفاء" : "المزيد"}
              </Button>
            </div>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="border-t pt-4 space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    ترتيب حسب
                  </label>
                  <Select
                    value={sortBy}
                    onValueChange={(value) => setSortBy(value as SortOption)}
                  >
                    <SelectTrigger className="h-10">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">الاسم</SelectItem>
                      <SelectItem value="attendance_rate">
                        معدل الحضور
                      </SelectItem>
                      <SelectItem value="recent_activity">آخر نشاط</SelectItem>
                      <SelectItem value="year">السنة الدراسية</SelectItem>
                      <SelectItem value="college">الكلية</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    اتجاه الترتيب
                  </label>
                  <Select
                    value={sortDirection}
                    onValueChange={(value) =>
                      setSortDirection(value as SortDirection)
                    }
                  >
                    <SelectTrigger className="h-10">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="asc">
                        <div className="flex items-center gap-2">
                          <SortAsc className="h-4 w-4" />
                          تصاعدي
                        </div>
                      </SelectItem>
                      <SelectItem value="desc">
                        <div className="flex items-center gap-2">
                          <SortDesc className="h-4 w-4" />
                          تنازلي
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-end">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchQuery("");
                      setFilterStatus("all");
                      setFilterYear("all");
                      setFilterCollege("all");
                      setSortBy("name");
                      setSortDirection("asc");
                    }}
                    className="w-full h-10"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    إعادة تعيين
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Enhanced Results Summary */}
          <div
            className={cn(
              "flex items-center justify-between text-sm p-3 rounded-lg transition-colors",
              searchQuery ? "bg-blue-50 border border-blue-200" : "bg-gray-50"
            )}
          >
            <div className="flex items-center gap-2">
              <span className="font-medium">
                {searchQuery
                  ? `وُجد ${filteredAndSortedUsers.length} نتيجة من أصل ${users.length} عضو`
                  : `عرض ${filteredAndSortedUsers.length} من ${users.length} عضو`}
              </span>
              {searchQuery && filteredAndSortedUsers.length === 0 && (
                <span className="text-red-600">- لا توجد نتائج مطابقة</span>
              )}
            </div>
            <div className="flex items-center gap-2">
              {searchQuery && (
                <Badge variant="outline" className="bg-blue-100 text-blue-800">
                  <Search className="h-3 w-3 mr-1" />
                  بحث نشط
                </Badge>
              )}
              {(filterStatus !== "all" ||
                filterYear !== "all" ||
                filterCollege !== "all") && (
                <Badge variant="secondary">مرشحات مطبقة</Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Operations */}
      {filteredAndSortedUsers.length > 0 && (
        <Card className="animate-fade-in">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              العمليات المجمعة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {/* تسجيل الكل حاضر - Mark All Present */}
              <Button
                onClick={handleBulkMarkPresent}
                className="btn-gradient"
                disabled={bulkActionLoading}
              >
                {bulkActionLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <UserCheck className="h-4 w-4 mr-2" />
                )}
                تسجيل الكل حاضر ({filteredAndSortedUsers.length})
              </Button>

              {/* تسجيل الكل غائب - Mark All Absent */}
              <Button
                onClick={() => {
                  const userIds = filteredAndSortedUsers.map((u) => u.id);
                  setConfirmAction({
                    type: "bulk_absent",
                    userIds,
                    message: `هل أنت متأكد من تسجيل غياب جميع الأعضاء (${userIds.length} عضو)؟`,
                  });
                  setShowConfirmDialog(true);
                }}
                variant="outline"
                disabled={bulkActionLoading}
              >
                <UserX className="h-4 w-4 mr-2" />
                تسجيل الكل غائب ({filteredAndSortedUsers.length})
              </Button>

              {/* تسجيل الباقي حاضر - Mark Remaining Present */}
              <Button
                onClick={() => {
                  const notMarkedUsers = filteredAndSortedUsers.filter(
                    (u) => !dateAttendance.some((a) => a.user_id === u.id)
                  );
                  if (notMarkedUsers.length > 0) {
                    const userIds = notMarkedUsers.map((u) => u.id);
                    setConfirmAction({
                      type: "bulk_present",
                      userIds,
                      message: `هل أنت متأكد من تسجيل حضور الأعضاء غير المسجلين (${userIds.length} عضو)؟`,
                    });
                    setShowConfirmDialog(true);
                  }
                }}
                variant="outline"
                disabled={
                  bulkActionLoading ||
                  filteredAndSortedUsers.filter(
                    (u) => !dateAttendance.some((a) => a.user_id === u.id)
                  ).length === 0
                }
              >
                <UserPlus className="h-4 w-4 mr-2" />
                تسجيل الباقي حاضر (
                {
                  filteredAndSortedUsers.filter(
                    (u) => !dateAttendance.some((a) => a.user_id === u.id)
                  ).length
                }
                )
              </Button>

              {/* تسجيل الباقي غائب - Mark Remaining Absent */}
              <Button
                onClick={() => {
                  const notMarkedUsers = filteredAndSortedUsers.filter(
                    (u) => !dateAttendance.some((a) => a.user_id === u.id)
                  );
                  if (notMarkedUsers.length > 0) {
                    const userIds = notMarkedUsers.map((u) => u.id);
                    setConfirmAction({
                      type: "bulk_absent",
                      userIds,
                      message: `هل أنت متأكد من تسجيل غياب الأعضاء غير المسجلين (${userIds.length} عضو)؟`,
                    });
                    setShowConfirmDialog(true);
                  }
                }}
                variant="outline"
                disabled={
                  bulkActionLoading ||
                  filteredAndSortedUsers.filter(
                    (u) => !dateAttendance.some((a) => a.user_id === u.id)
                  ).length === 0
                }
              >
                <UserX className="h-4 w-4 mr-2" />
                تسجيل الباقي غائب (
                {
                  filteredAndSortedUsers.filter(
                    (u) => !dateAttendance.some((a) => a.user_id === u.id)
                  ).length
                }
                )
              </Button>

              {/* مسح الكل - Clear All */}
              <Button
                onClick={() => {
                  setConfirmAction({
                    type: "clear_all",
                    message: `هل أنت متأكد من مسح جميع تسجيلات الحضور لتاريخ ${selectedDate}؟`,
                  });
                  setShowConfirmDialog(true);
                }}
                variant="destructive"
                disabled={bulkActionLoading || dateAttendance.length === 0}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                مسح الكل ({dateAttendance.length})
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 animate-scale-in">
        <Card className="hover-lift">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي الأعضاء
            </CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{attendanceStats.total}</div>
            <p className="text-xs text-muted-foreground">عضو مسجل</p>
          </CardContent>
        </Card>

        <Card className="hover-lift">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الحاضرون</CardTitle>
            <UserCheck className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {attendanceStats.present}
            </div>
            <p className="text-xs text-muted-foreground">
              {attendanceStats.attendanceRate}% معدل الحضور
            </p>
          </CardContent>
        </Card>

        <Card className="hover-lift">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الغائبون</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {attendanceStats.absent}
            </div>
            <p className="text-xs text-muted-foreground">عضو غائب</p>
          </CardContent>
        </Card>

        <Card className="hover-lift">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              المعدل الأسبوعي
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {weeklyStats.weeklyRate}%
            </div>
            <p className="text-xs text-muted-foreground">
              {weeklyStats.weeklyPresent} من {weeklyStats.weeklyTotal}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Attendance Methods */}
      <Card className="animate-slide-in-right">
        <CardHeader>
          <CardTitle
            className={cn(
              "flex items-center gap-2",
              isRTL ? "flex-row-reverse" : "flex-row"
            )}
          >
            <CheckCircle className="h-5 w-5" />
            طرق تسجيل الحضور
          </CardTitle>
          <CardDescription>اختر الطريقة المناسبة لتسجيل الحضور</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="list" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger
                value="manual"
                className={cn(
                  "flex items-center gap-2",
                  isRTL ? "flex-row-reverse" : "flex-row"
                )}
              >
                <Users className="h-4 w-4" />
                إدخال يدوي
              </TabsTrigger>
              <TabsTrigger
                value="qr"
                className={cn(
                  "flex items-center gap-2",
                  isRTL ? "flex-row-reverse" : "flex-row"
                )}
              >
                <QrCode className="h-4 w-4" />
                مسح QR
              </TabsTrigger>
              <TabsTrigger
                value="list"
                className={cn(
                  "flex items-center gap-2",
                  isRTL ? "flex-row-reverse" : "flex-row"
                )}
              >
                <ClipboardCheck className="h-4 w-4" />
                قائمة شاملة
                {searchQuery && (
                  <Badge variant="secondary" className="text-xs ml-1">
                    {filteredAndSortedUsers.length}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="manual" className="space-y-4">
              <div className="space-y-4">
                <Input
                  placeholder="أدخل رقم الهاتف أو معرف العضو..."
                  value={manualId}
                  onChange={(e) => setManualId(e.target.value)}
                  className="h-12"
                />
                <div
                  className={cn(
                    "flex gap-2",
                    isRTL ? "flex-row-reverse" : "flex-row"
                  )}
                >
                  <Button
                    onClick={handleManualIdSubmit}
                    disabled={!manualId}
                    className="flex-1 btn-gradient h-12"
                  >
                    تسجيل حضور
                  </Button>
                  <Button
                    onClick={() => {
                      const foundUser = users.find(
                        (u) => u.id === manualId || u.phone === manualId
                      );
                      if (foundUser) handleMarkAttendance(foundUser.id, false);
                    }}
                    disabled={!manualId}
                    variant="outline"
                    className="flex-1 h-12"
                  >
                    تسجيل غياب
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="qr" className="space-y-4">
              <div className="text-center space-y-4">
                <div className="w-48 h-48 bg-gray-100 rounded-lg mx-auto flex items-center justify-center">
                  <Camera className="h-24 w-24 text-gray-400" />
                </div>
                <p className="text-gray-600">اضغط لفتح الكاميرا ومسح QR Code</p>
                <Button className="btn-gradient">
                  <Camera className="h-4 w-4 mr-2" />
                  فتح الكاميرا
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="list" className="space-y-4">
              {/* Modern Responsive Table Layout */}
              <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gray-50/50">
                        <TableHead className="w-16 text-center">
                          الصورة
                        </TableHead>
                        <TableHead className="min-w-[200px]">
                          الاسم الكامل
                        </TableHead>
                        <TableHead className="min-w-[120px] hidden sm:table-cell">
                          رقم الهاتف
                        </TableHead>
                        <TableHead className="min-w-[150px] hidden md:table-cell">
                          الكلية
                        </TableHead>
                        <TableHead className="w-20 text-center hidden lg:table-cell">
                          السنة
                        </TableHead>
                        <TableHead className="w-32 text-center">
                          حالة الحضور
                        </TableHead>
                        <TableHead className="w-40 text-center">
                          الإجراءات
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAndSortedUsers.map((user, index) => {
                        const isPresent = isUserPresent(user.id);
                        const isAbsent = isUserAbsent(user.id);
                        const attendanceRecord = dateAttendance.find(
                          (a) => a.user_id === user.id
                        );
                        const isLoading = actionLoading === user.id;

                        return (
                          <TableRow
                            key={generateUniqueKey(
                              "table-user",
                              user.id,
                              selectedDate,
                              index
                            )}
                            className={cn(
                              "transition-all duration-200 hover:bg-gray-50/50",
                              isPresent &&
                                "bg-green-50/30 hover:bg-green-50/50",
                              isAbsent && "bg-red-50/30 hover:bg-red-50/50"
                            )}
                          >
                            {/* Avatar Column */}
                            <TableCell className="text-center">
                              <div className="flex justify-center">
                                <div className="relative">
                                  <div
                                    className={cn(
                                      "w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm",
                                      isPresent
                                        ? "bg-gradient-to-r from-green-500 to-emerald-500"
                                        : isAbsent
                                        ? "bg-gradient-to-r from-red-500 to-rose-500"
                                        : "bg-gradient-to-r from-blue-500 to-purple-500"
                                    )}
                                  >
                                    {user.name.charAt(0)}
                                  </div>
                                  {/* Status Indicator */}
                                  <div
                                    className={cn(
                                      "absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white",
                                      isPresent
                                        ? "bg-green-500"
                                        : isAbsent
                                        ? "bg-red-500"
                                        : "bg-gray-400"
                                    )}
                                  />
                                </div>
                              </div>
                            </TableCell>

                            {/* Name Column */}
                            <TableCell>
                              <div className="space-y-1">
                                <div className="font-semibold text-gray-900">
                                  {highlightText(
                                    user.name,
                                    debouncedSearchQuery
                                  )}
                                </div>

                                {/* Mobile-only info */}
                                <div className="sm:hidden space-y-1">
                                  <div className="flex items-center gap-2 text-xs text-gray-600">
                                    <Phone className="h-3 w-3" />
                                    <span className="font-mono">
                                      {highlightText(
                                        user.phone,
                                        debouncedSearchQuery
                                      )}
                                    </span>
                                  </div>
                                  <div className="md:hidden flex items-center gap-2 text-xs text-gray-600">
                                    <GraduationCap className="h-3 w-3" />
                                    <span>
                                      {highlightText(
                                        user.college,
                                        debouncedSearchQuery
                                      )}
                                    </span>
                                  </div>
                                  <div className="lg:hidden">
                                    <Badge
                                      variant="secondary"
                                      className="text-xs"
                                    >
                                      السنة {user.year}
                                    </Badge>
                                  </div>
                                </div>

                                {user.attendanceRate !== undefined && (
                                  <Badge variant="outline" className="text-xs">
                                    {Math.round(user.attendanceRate)}% حضور
                                  </Badge>
                                )}
                                {attendanceRecord && (
                                  <div className="text-xs text-gray-500">
                                    تم التسجيل في{" "}
                                    {format(
                                      new Date(attendanceRecord.created_at),
                                      "HH:mm"
                                    )}
                                  </div>
                                )}
                              </div>
                            </TableCell>

                            {/* Phone Column */}
                            <TableCell className="hidden sm:table-cell">
                              <div className="flex items-center gap-2 text-gray-700">
                                <Phone className="h-4 w-4 text-gray-400" />
                                <span className="font-mono">
                                  {highlightText(
                                    user.phone,
                                    debouncedSearchQuery
                                  )}
                                </span>
                              </div>
                            </TableCell>

                            {/* College Column */}
                            <TableCell className="hidden md:table-cell">
                              <div className="flex items-center gap-2 text-gray-700">
                                <GraduationCap className="h-4 w-4 text-gray-400" />
                                <span>
                                  {highlightText(
                                    user.college,
                                    debouncedSearchQuery
                                  )}
                                </span>
                              </div>
                            </TableCell>

                            {/* Year Column */}
                            <TableCell className="text-center hidden lg:table-cell">
                              <Badge variant="secondary" className="text-xs">
                                السنة {user.year}
                              </Badge>
                            </TableCell>

                            {/* Status Column */}
                            <TableCell className="text-center">
                              {isPresent ? (
                                <Badge className="bg-green-100 text-green-800 border-green-200">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  حاضر
                                </Badge>
                              ) : isAbsent ? (
                                <Badge
                                  variant="destructive"
                                  className="bg-red-100 text-red-800 border-red-200"
                                >
                                  <X className="h-3 w-3 mr-1" />
                                  غائب
                                </Badge>
                              ) : (
                                <Badge
                                  variant="outline"
                                  className="text-gray-600"
                                >
                                  غير مسجل
                                </Badge>
                              )}
                            </TableCell>

                            {/* Actions Column */}
                            <TableCell className="text-center">
                              <div className="flex items-center justify-center gap-1">
                                {isPresent ? (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() =>
                                      handleMarkAttendance(user.id, false)
                                    }
                                    disabled={isLoading}
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
                                    title="تسجيل غياب"
                                  >
                                    {isLoading ? (
                                      <Loader2 className="h-3 w-3 animate-spin" />
                                    ) : (
                                      <UserX className="h-3 w-3" />
                                    )}
                                  </Button>
                                ) : isAbsent ? (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() =>
                                      handleMarkAttendance(user.id, true)
                                    }
                                    disabled={isLoading}
                                    className="text-green-600 hover:text-green-700 hover:bg-green-50 border-green-200"
                                    title="تسجيل حضور"
                                  >
                                    {isLoading ? (
                                      <Loader2 className="h-3 w-3 animate-spin" />
                                    ) : (
                                      <UserCheck className="h-3 w-3" />
                                    )}
                                  </Button>
                                ) : (
                                  <div className="flex gap-1">
                                    <Button
                                      size="sm"
                                      onClick={() =>
                                        handleMarkAttendance(user.id, true)
                                      }
                                      disabled={isLoading}
                                      className="bg-green-600 hover:bg-green-700 text-white px-2"
                                      title="تسجيل حضور"
                                    >
                                      {isLoading ? (
                                        <Loader2 className="h-3 w-3 animate-spin" />
                                      ) : (
                                        <CheckCircle className="h-3 w-3" />
                                      )}
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() =>
                                        handleMarkAttendance(user.id, false)
                                      }
                                      disabled={isLoading}
                                      className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 px-2"
                                      title="تسجيل غياب"
                                    >
                                      <X className="h-3 w-3" />
                                    </Button>
                                  </div>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}

                      {/* Empty State Row */}
                      {filteredAndSortedUsers.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={7} className="h-64">
                            <div className="text-center py-12">
                              {searchQuery ? (
                                <>
                                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                                    لا توجد نتائج للبحث "{searchQuery}"
                                  </h3>
                                  <div className="text-gray-500 space-y-2">
                                    <p>جرب:</p>
                                    <ul className="text-sm space-y-1">
                                      <li>• التأكد من صحة الكتابة</li>
                                      <li>
                                        • استخدام كلمات أقل أو أكثر عمومية
                                      </li>
                                      <li>
                                        • البحث برقم الهاتف أو جزء من الاسم
                                      </li>
                                      <li>• إزالة المرشحات المطبقة</li>
                                    </ul>
                                  </div>
                                  <Button
                                    variant="outline"
                                    onClick={() => setSearchQuery("")}
                                    className="mt-4"
                                  >
                                    مسح البحث
                                  </Button>
                                </>
                              ) : (
                                <>
                                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                                    لا توجد أعضاء
                                  </h3>
                                  <p className="text-gray-500">
                                    جرب تعديل المرشحات أو إضافة أعضاء جدد
                                  </p>
                                </>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Recent Attendance */}
      {recentAttendance.length > 0 && (
        <Card className="animate-fade-in">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              آخر التسجيلات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {recentAttendance.map((userId, index) => {
                const user = users.find((u) => u.id === userId);
                if (!user) return null;

                return (
                  <div
                    key={generateUniqueKey(
                      "recent-attendance",
                      userId,
                      selectedDate,
                      index
                    )}
                    className="flex items-center gap-3 p-2 bg-green-50 rounded-lg"
                  >
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-medium">{user.name}</span>
                    <Badge variant="outline">تم التسجيل</Badge>
                    <span className="text-xs text-gray-500 mr-auto">
                      {format(new Date(), "HH:mm")}
                    </span>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Attendance Details Modal */}
      {showAttendanceDetails && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-4xl animate-scale-in max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  تفاصيل الحضور -{" "}
                  {format(new Date(selectedDate), "dd MMMM yyyy", {
                    locale: ar,
                  })}
                </CardTitle>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowAttendanceDetails(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {attendanceStats.present}
                  </div>
                  <div className="text-sm text-green-700">حاضر</div>
                </div>
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {attendanceStats.absent}
                  </div>
                  <div className="text-sm text-red-700">غائب</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {attendanceStats.attendanceRate}%
                  </div>
                  <div className="text-sm text-blue-700">معدل الحضور</div>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="font-semibold text-lg">
                  قائمة الحضور التفصيلية
                </h3>
                {dateAttendance.map((record, index) => (
                  <div
                    key={generateUniqueKey(
                      "attendance-details",
                      record.id,
                      record.date,
                      index
                    )}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className={cn(
                          "w-3 h-3 rounded-full",
                          record.present ? "bg-green-500" : "bg-red-500"
                        )}
                      />
                      <span className="font-medium">{record.user_name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={record.present ? "default" : "destructive"}
                      >
                        {record.present ? "حاضر" : "غائب"}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {format(new Date(record.created_at), "HH:mm")}
                      </span>
                    </div>
                  </div>
                ))}
                {dateAttendance.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد سجلات حضور لهذا التاريخ
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>تأكيد العملية</AlertDialogTitle>
            <AlertDialogDescription>
              {confirmAction?.message}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction
              onClick={async () => {
                if (!confirmAction || !user?.email) return;

                setBulkActionLoading(true);

                try {
                  switch (confirmAction.type) {
                    case "bulk_present":
                      if (confirmAction.userIds) {
                        bulkMarkAttendance(
                          confirmAction.userIds,
                          true,
                          user.email
                        );
                        toast.success(
                          `تم تسجيل حضور ${confirmAction.userIds.length} عضو بنجاح`
                        );
                      }
                      break;
                    case "bulk_absent":
                      if (confirmAction.userIds) {
                        bulkMarkAttendance(
                          confirmAction.userIds,
                          false,
                          user.email
                        );
                        toast.success(
                          `تم تسجيل غياب ${confirmAction.userIds.length} عضو بنجاح`
                        );
                      }
                      break;
                    case "clear_all":
                      // Clear all attendance for the selected date
                      clearAttendanceForDate(selectedDate);
                      toast.success(
                        `تم مسح جميع تسجيلات الحضور لتاريخ ${selectedDate} بنجاح`
                      );
                      break;
                  }
                } catch (error) {
                  toast.error("حدث خطأ أثناء تنفيذ العملية");
                } finally {
                  setBulkActionLoading(false);
                  setShowConfirmDialog(false);
                  setConfirmAction(null);
                }
              }}
              disabled={bulkActionLoading}
            >
              {bulkActionLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  جاري التنفيذ...
                </>
              ) : (
                "تأكيد"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Keyboard Shortcuts Dialog */}
      <Dialog
        open={showKeyboardShortcuts}
        onOpenChange={setShowKeyboardShortcuts}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Keyboard className="h-5 w-5" />
              اختصارات لوحة المفاتيح
            </DialogTitle>
            <DialogDescription>
              استخدم هذه الاختصارات لتسريع عملك
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">البحث</span>
                <div className="flex gap-1">
                  <Badge variant="outline">Ctrl + F</Badge>
                  <Badge variant="outline">/</Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">تسجيل الكل حاضر</span>
                <Badge variant="outline">Ctrl + A</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">تسجيل الكل غائب</span>
                <Badge variant="outline">Ctrl + Shift + A</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">إعادة تعيين المرشحات</span>
                <Badge variant="outline">Ctrl + R</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">إغلاق النوافذ / مسح البحث</span>
                <Badge variant="outline">Escape</Badge>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowKeyboardShortcuts(false)}>
              إغلاق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Mobile Floating Action Button */}
      <div className="fixed bottom-6 right-6 md:hidden z-40">
        <div className="flex flex-col gap-3">
          {/* Quick Search FAB */}
          <Button
            size="icon"
            className="w-12 h-12 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg"
            onClick={() => searchInputRef.current?.focus()}
          >
            <Search className="h-5 w-5" />
          </Button>

          {/* Quick Mark All Present FAB */}
          <Button
            size="icon"
            className="w-12 h-12 rounded-full bg-green-600 hover:bg-green-700 shadow-lg"
            onClick={handleBulkMarkPresent}
            disabled={bulkActionLoading || filteredAndSortedUsers.length === 0}
          >
            {bulkActionLoading ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <UserCheck className="h-5 w-5" />
            )}
          </Button>
        </div>
      </div>

      {/* Mobile-optimized bottom sheet for quick actions */}
      {/* This could be implemented as a sheet component for better mobile UX */}
    </div>
  );
}
