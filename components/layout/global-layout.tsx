"use client"

import type React from "react"
import { useAuth } from "@/contexts/auth-context"
import { useRTL } from "@/contexts/rtl-context"
import { Sidebar } from "./sidebar"
import { Loader2 } from "lucide-react"
import { useRouter, usePathname } from "next/navigation"
import { useEffect } from "react"
import { cn } from "@/lib/utils"

export function GlobalLayout({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth()
  const { isRTL, direction } = useRTL()
  const router = useRouter()
  const pathname = usePathname()

  // Don't show sidebar on login page
  const isLoginPage = pathname === "/login"

  useEffect(() => {
    if (!loading && !user && !isLoginPage) {
      router.push("/login")
    }
  }, [user, loading, router, isLoginPage])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-gray-600 font-cairo">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user && !isLoginPage) {
    return null
  }

  // If it's login page, render without sidebar
  if (isLoginPage) {
    return <>{children}</>
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50" dir={direction}>
      <Sidebar />
      <div
        className={cn(
          "transition-all duration-300 ease-in-out-cubic min-h-screen",
          // Desktop margins
          isRTL ? "md:mr-72" : "md:ml-72",
        )}
      >
        <main className="min-h-screen">{children}</main>
      </div>
    </div>
  )
}
